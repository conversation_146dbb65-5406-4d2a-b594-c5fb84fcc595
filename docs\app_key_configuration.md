# 千义听悟 AppKey 配置指南

本文档介绍如何配置千义听悟服务所需的 AppKey。

## 什么是 AppKey

AppKey 是阿里云千义听悟服务的应用密钥，用于身份验证和访问控制。每个应用都有一个唯一的 AppKey，需要在调用千义听悟 API 时提供。

## 配置方法

### 1. 配置文件方式

将 AppKey 添加到配置文件中。支持以下配置文件路径（按优先级排序）：

- `~/.alibabacloud/credentials.ini`（推荐）
- `~/.aliyun/config.json`
- `./config/credentials.conf`

在配置文件中添加 `app_key` 配置项：

```ini
[default]
type = access_key
access_key_id = your_access_key_id
access_key_secret = your_access_key_secret
region_id = cn-beijing
app_key = your_app_key
```

### 2. 环境变量方式

通过环境变量设置 AppKey：

```bash
# Windows
set TINGWU_APP_KEY=your_app_key

# Linux/macOS
export TINGWU_APP_KEY=your_app_key
```

### 3. 命令行参数方式

使用命令行工具时，可以通过 `--app-key` 参数指定：

```bash
python -m tingwu_server.cli --app-key your_app_key --task-key test --file-url http://example.com/audio.mp3
```

## 配置优先级

1. 命令行参数（最高优先级）
2. 环境变量
3. 配置文件（最低优先级）

## 注意事项

- AppKey 属于敏感信息，请妥善保管，不要将其硬编码在代码中或提交到版本控制系统
- 建议在生产环境中使用环境变量或配置文件方式配置 AppKey
- 如果未配置 AppKey，程序将抛出错误提示