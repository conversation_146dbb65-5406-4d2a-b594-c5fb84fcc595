# AI Hub服务器

这是一个基于FastAPI的HTTP服务器，用于调用阿里云千义听悟API进行音频文件离线转写和文本聊天服务。服务器提供RESTful API接口，支持多种音频格式的语音转文本功能和大模型文本交互功能。

## 功能特性

- 🎵 **多格式支持**: 支持MP3、WAV、M4A、AAC、FLAC等多种音频格式
- 🌐 **HTTP接口**: 提供标准的RESTful API接口
- 🔧 **灵活配置**: 支持多种转写参数配置（语言、采样率、说话人分离等）
- 📝 **详细日志**: 完整的请求和响应日志记录
- 🛠️ **命令行工具**: 提供CLI工具用于测试和调试
- 🚀 **生产就绪**: 使用Uvicorn ASGI服务器，适合生产环境部署

## 系统要求

- Python 3.10.18 或更高版本
- 阿里云账号和访问凭证（AccessKey ID 和 AccessKey Secret）
- 网络连接（用于访问阿里云API和音频文件）

## 快速开始

### 1. 安装依赖

```bash
# 安装Python依赖包
pip install -r requirements.txt

# 或者使用setup.py安装
python setup.py install
```

### 2. 配置阿里云凭证

在使用服务之前，需要配置阿里云访问凭证和千义听悟 AppKey。推荐使用环境变量方式：

```bash
# Windows
set ALIBABA_CLOUD_ACCESS_KEY_ID=your_access_key_id
set ALIBABA_CLOUD_ACCESS_KEY_SECRET=your_access_key_secret
set TINGWU_APP_KEY=your_app_key

# Linux/macOS
export ALIBABA_CLOUD_ACCESS_KEY_ID=your_access_key_id
export ALIBABA_CLOUD_ACCESS_KEY_SECRET=your_access_key_secret
export TINGWU_APP_KEY=your_app_key
```

或者创建配置文件 `./config/credentials.conf`：

```ini
[default]
# 阿里云访问凭证
access_key_id = your_access_key_id
access_key_secret = your_access_key_secret
region_id = cn-beijing
# 千义听悟项目的AppKey
app_key = your_app_key
```

详细的 AppKey 配置说明请参考 [AppKey 配置指南](docs/app_key_configuration.md)。

### 3. 启动服务器

#### 开发环境

```bash
# 直接运行FastAPI应用（开发模式）
python -m app.dev
```

#### 生产环境

```bash
# 使用Uvicorn ASGI服务器（推荐）
python -m app.prod

# 或者使用安装后的命令
aihub-server
```

服务器默认监听端口 `5579`，可以通过环境变量配置：

```bash
# 自定义端口和主机
set AIHUB_HOST=0.0.0.0
set AIHUB_PORT=8080
set AIHUB_THREADS=8
```

### 4. 测试服务

#### 健康检查

```bash
curl http://localhost:5579/health
```

响应示例：
```json
{
  "status": "healthy",
  "service": "aihub-server",
  "version": "1.0.0"
}
```

#### 创建转写任务

```bash
curl -X POST http://localhost:5579/ai/aliyun/voice/tingwu/new \
  -H "Content-Type: application/json" \
  -d '{
    "TaskKey": "task_tingwu_123",
    "fileUrl": "http://example.com/audio.mp3"
  }'
```

## API接口文档

### POST /ai/aliyun/voice/tingwu/new

创建音频转写任务。

#### 请求参数

| 参数名 | 类型 | 必需 | 默认值 | 说明 |
|--------|------|------|--------|------|
| TaskKey | string | 是 | - | 任务唯一标识符，只能包含字母、数字、下划线和连字符 |
| fileUrl | string | 是 | - | 音频文件的HTTP访问地址 |
| sourceLanguage | string | 否 | "cn" | 源语言（cn=中文, en=英文, ja=日文等） |
| sampleRate | integer | 否 | 16000 | 音频采样率（8000-48000Hz） |
| speakerCount | integer | 否 | 0 | 说话人数量（0=自动检测，1-20=指定数量） |
| outputLevel | integer | 否 | 1 | 输出级别（1-3） |
| diarizationEnabled | boolean | 否 | true | 是否启用说话人分离 |
| progressiveCallbacksEnabled | boolean | 否 | true | 是否启用进度回调 |
| translationEnabled | boolean | 否 | false | 是否启用翻译 |
| autoChaptersEnabled | boolean | 否 | false | 是否启用自动章节 |
| meetingAssistanceEnabled | boolean | 否 | false | 是否启用会议助手 |
| summarizationEnabled | boolean | 否 | false | 是否启用摘要 |

#### 请求示例

```json
{
  "TaskKey": "task_tingwu_123",
  "fileUrl": "http://example.com/audio.mp3",
  "sourceLanguage": "cn",
  "sampleRate": 16000,
  "speakerCount": 2,
  "diarizationEnabled": true,
  "translationEnabled": false
}
```

#### 响应格式

成功响应：
```json
{
  "Code": "0",
  "Message": "success",
  "Data": {
    "TaskId": "3c3aa2f481554a54ae7f43fa7a02a8c0",
    "TaskKey": "task_tingwu_123",
    "TaskStatus": "ONGOING"
  },
  "RequestId": "D2144329-4D91-56E9-BEC4-DC6C99E7727C"
}
```

错误响应：
```json
{
  "Code": "400",
  "Message": "缺少必需字段: TaskKey",
  "Data": null,
  "RequestId": null
}
```

#### 任务状态说明

| 状态 | 说明 |
|------|------|
| ONGOING | 进行中 |
| COMPLETED | 已完成 |
| FAILED | 失败 |
| PENDING | 等待中 |

## 命令行工具

项目提供了命令行工具用于测试和调试：

### 交互式模式

```bash
# 启动交互式CLI工具
python -m tingwu_server.cli_tool

# 或者使用安装后的命令
aihub-cli
```

### 直接创建任务

```bash
# 创建转写任务
python -m app.cli_tool \
  --task-key my_task_123 \
  --file-url http://example.com/audio.mp3 \
  --source-language cn \
  --sample-rate 16000
```

### 测试API连接

```bash
# 测试与千义听悟API的连接
python -m app.cli_tool --test
```

### CLI参数说明

| 参数 | 说明 |
|------|------|
| --task-key | 任务唯一标识符 |
| --file-url | 音频文件URL |
| --source-language | 源语言（默认：cn） |
| --sample-rate | 采样率（默认：16000） |
| --speaker-count | 说话人数量（默认：0） |
| --app-key | 阿里云应用密钥 |
| --test | 测试API连接 |
| --no-diarization | 禁用说话人分离 |
| --enable-translation | 启用翻译 |

## 项目结构

```
app/
├── __init__.py          # 包初始化文件
├── app.py              # FastAPI应用主文件
├── prod.py             # 生产环境服务器启动脚本
├── tingwu.py           # 千义听悟API客户端封装
├── logger.py           # 日志配置模块
├── config_manager.py   # 配置管理模块
├── utils.py            # 工具函数模块
└── cli_tool.py         # 命令行工具

requirements.txt        # Python依赖包列表
setup.py               # 项目安装脚本
README.md              # 项目文档
```

## 配置说明

### 配置系统

本项目采用多层次配置管理系统，支持多环境部署和灵活的配置方式：

#### 配置优先级
1. **环境变量** (最高优先级)
2. **配置文件** (中等优先级)
3. **默认值** (最低优先级)

#### 环境配置
通过 `AIHUB_ENVIRONMENT` 环境变量指定运行环境：
- `development` - 开发环境
- `testing` - 测试环境
- `production` - 生产环境 (默认)

#### 配置文件
系统会按以下顺序搜索配置文件：
1. `./config/credentials.conf` (当前目录)
2. `~/.config/aihub/credentials.conf` (用户配置目录)
3. `/etc/aihub/credentials.conf` (系统配置目录)

配置文件采用INI格式，详细说明请参考 [配置指南](docs/configuration.md)。

### 环境变量

#### 核心配置
| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| AIHUB_ENVIRONMENT | production | 运行环境 (development/testing/production) |
| AIHUB_HOST | 0.0.0.0 | 服务器监听主机 |
| AIHUB_PORT | 5579 | 服务器监听端口 |
| AIHUB_THREADS | 4 | 工作线程数 |

#### 阿里云凭证
| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| ALIBABA_CLOUD_ACCESS_KEY_ID | - | 阿里云访问密钥ID |
| ALIBABA_CLOUD_ACCESS_KEY_SECRET | - | 阿里云访问密钥Secret |
| ALIBABA_CLOUD_REGION | cn-beijing | 阿里云区域 |
| AIHUB_APP_KEY | - | 千义听悟项目的AppKey |

#### 高级配置
| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| AIHUB_CONNECTION_TIMEOUT | 30 | 连接超时时间(秒) |
| AIHUB_READ_TIMEOUT | 300 | 读取超时时间(秒) |
| AIHUB_MAX_RETRIES | 3 | 最大重试次数 |
| AIHUB_RETRY_BACKOFF | 2.0 | 重试退避乘数 |

### 支持的音频格式

| 格式 | 扩展名 | 说明 |
|------|--------|------|
| MP3 | .mp3 | 最常用的音频格式 |
| WAV | .wav | 无损音频格式 |
| M4A | .m4a | Apple音频格式 |
| AAC | .aac | 高级音频编码 |
| FLAC | .flac | 无损压缩音频 |
| OGG | .ogg | 开源音频格式 |
| WMA | .wma | Windows媒体音频 |
| MP4 | .mp4 | 视频文件（提取音频） |

### 支持的语言

| 语言代码 | 语言名称 |
|----------|----------|
| cn | 中文 |
| en | 英文 |
| ja | 日文 |
| ko | 韩文 |
| es | 西班牙文 |
| fr | 法文 |
| de | 德文 |
| it | 意大利文 |
| pt | 葡萄牙文 |
| ru | 俄文 |

## 错误处理

### 常见错误码

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 400 | 请求参数错误 | 检查请求参数格式和必需字段 |
| 401 | 认证失败 | 检查阿里云访问凭证配置 |
| 404 | 接口不存在 | 检查请求URL路径 |
| 405 | 请求方法不允许 | 使用正确的HTTP方法（POST） |
| 500 | 服务器内部错误 | 查看服务器日志，检查网络连接 |

### 日志配置

服务器使用结构化日志记录，支持多种输出方式：

#### 日志文件
- **应用日志**: `logs/aihub-server-YYYY-MM-DD.log` - 按天分割的应用运行日志
- **自动清理**: 自动删除超过保留天数的旧日志文件
- **脱敏处理**: 敏感信息（如文件URL、API密钥）会被自动脱敏

#### 日志级别
- **DEBUG**: 详细调试信息
- **INFO**: 一般信息（默认）
- **WARNING**: 警告信息
- **ERROR**: 错误信息
- **CRITICAL**: 严重错误

#### 日志配置
```bash
# 设置日志级别
export LOG_LEVEL=INFO

# 设置日志目录
export LOG_DIR=logs

# 设置日志文件保留天数
export LOG_BACKUP_COUNT=30
```

#### 查看日志
```bash
# 查看今天的日志
tail -f logs/aihub-server-$(date +%Y-%m-%d).log

# 查看所有日志文件
ls -la logs/

# 使用journalctl（systemd服务）
sudo journalctl -u aihub-server -f

# 搜索特定内容
grep "ERROR" logs/aihub-server-*.log
```

## 性能优化

### 生产环境建议

1. **使用反向代理**: 在生产环境中建议使用Nginx等反向代理服务器
2. **调整线程数**: 根据服务器性能调整 `AIHUB_THREADS` 环境变量
3. **监控资源**: 监控CPU、内存和网络使用情况
4. **日志轮转**: 配置日志轮转避免日志文件过大

### 并发处理

服务器使用Uvicorn ASGI服务器，支持异步并发处理请求。默认配置：
- 单进程模式（可通过外部工具管理多进程）
- 异步处理请求
- 支持WebSocket连接

## 安全注意事项

1. **凭证安全**: 不要在代码中硬编码访问凭证
2. **网络安全**: 在生产环境中使用HTTPS
3. **访问控制**: 根据需要配置防火墙和访问控制
4. **输入验证**: 服务器已内置输入验证，但建议在客户端也进行验证

## 故障排除

### 常见问题

**Q: 服务器启动失败**
A: 检查端口是否被占用，确认Python版本和依赖包是否正确安装

**Q: API调用返回认证错误**
A: 检查阿里云访问凭证配置，确认AccessKey有效且有权限访问千义听悟服务

**Q: 音频文件无法访问**
A: 确认音频文件URL可以正常访问，检查网络连接和文件格式

**Q: 转写任务创建成功但无结果**
A: 转写是异步过程，需要等待一段时间。可以通过TaskId查询任务状态

### 调试模式

开发环境可以启用调试模式：

```bash
# 启用FastAPI调试模式
export FASTAPI_ENV=development
python -m app.dev
```

## 许可证

本项目使用 MIT 许可证。详见 LICENSE 文件。

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## Linux服务器部署

本项目支持在Linux服务器上进行生产环境部署，提供多种部署方式：

### 🚀 一键部署 (推荐)

```bash
# 上传项目文件到服务器
scp -r ./aihub user@your-server:/tmp/

# 运行自动部署脚本
cd /tmp/aihub/deploy
sudo chmod +x deploy.sh
sudo ./deploy.sh

# 配置阿里云凭证
sudo nano /opt/aihub-server/.env
# 填入: ALIBABA_CLOUD_ACCESS_KEY_ID=your_key
#      ALIBABA_CLOUD_ACCESS_KEY_SECRET=your_secret

# 重启服务
sudo systemctl restart aihub-server
```

### 🐳 Docker部署

```bash
# 使用Docker Compose
cp deploy/.env.example .env
nano .env  # 配置环境变量
docker-compose -f deploy/docker-compose.yml up -d

# 包含Nginx反向代理
docker-compose -f deploy/docker-compose.yml --profile with-nginx up -d
```

### 🔧 服务管理

```bash
# 使用管理脚本
./deploy/manage.sh start     # 启动服务
./deploy/manage.sh stop      # 停止服务
./deploy/manage.sh restart   # 重启服务
./deploy/manage.sh status    # 查看状态
./deploy/manage.sh logs      # 查看日志
./deploy/manage.sh test      # 测试API

# 或使用systemctl
sudo systemctl start aihub-server
sudo systemctl status aihub-server
sudo journalctl -u aihub-server -f
```

### 📁 部署文件说明

- `deploy/deploy.sh` - 自动部署脚本
- `deploy/manage.sh` - 服务管理脚本
- `deploy/aihub-server.service` - systemd服务文件
- `deploy/Dockerfile` - Docker镜像构建文件
- `deploy/docker-compose.yml` - Docker Compose配置
- `deploy/nginx.conf` - Nginx反向代理配置
- `deploy/.env.example` - 环境变量模板
- `deploy/README.md` - 详细部署文档

### 🌐 生产环境访问

部署完成后，服务将在以下地址可用：

- **直接访问**: `http://your-server:5579`
- **通过Nginx**: `http://your-server` (HTTP) 或 `https://your-server` (HTTPS)
- **健康检查**: `http://your-server:5579/health`
- **API接口**: `http://your-server:5579/ai/aliyun/voice/tingwu/new`

### 📊 监控和维护

```bash
# 查看服务状态
curl http://your-server:5579/health

# 查看系统资源
top
free -h
df -h

# 查看服务日志
tail -f /opt/aihub-server/logs/aihub-server.log
sudo journalctl -u aihub-server -f

# 备份配置
sudo cp /opt/aihub-server/.env /backup/
sudo cp /etc/systemd/system/aihub-server.service /backup/
```

详细的Linux部署文档请参考：[deploy/README.md](deploy/README.md)

## 故障排除

### 常见问题

#### TypeError: get_logger() takes 0 positional arguments but 1 was given

如果遇到此错误，说明 `logger.py` 中的 `get_logger()` 函数定义与调用不匹配。此问题已在最新版本中修复，`get_logger()` 函数现在接受一个可选的 `name` 参数。

解决方法：

1. 更新到最新版本
2. 或者手动修改 `app/logger.py` 文件，将 `get_logger()` 函数修改为：

```python
def get_logger(name: Optional[str] = None) -> logging.Logger:
    """
    获取全局日志记录器

    Args:
        name (Optional[str]): 日志记录器名称，默认为None

    Returns:
        logging.Logger: 日志记录器
    """
    global _global_logger

    if _global_logger is None:
        _global_logger = setup_logging()

    return _global_logger.get_logger()
```

#### 其他常见问题

- **无法连接到阿里云API**: 检查网络连接和凭证配置
- **音频文件无法访问**: 确保URL可公开访问或文件路径正确
- **转写结果不完整**: 检查音频质量和格式是否符合要求

## 相关链接

- [阿里云千义听悟官方文档](https://help.aliyun.com/document_detail/378659.html)
- [千义听悟API参考](https://next.api.aliyun.com/document/tingwu/2023-09-30/CreateTask)
- [FastAPI官方文档](https://fastapi.tiangolo.com/)
- [Uvicorn文档](https://www.uvicorn.org/)
- [Linux部署详细文档](deploy/README.md)
