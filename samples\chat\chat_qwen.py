from http import HTTPStatus
import dashscope


def call_with_messages():
    messages = [{"role":"system","content":"你是一个水平衡领域的AI助手"},
                {"role":"user","content":"根据末尾这段话整理出一个序列化后的json串, 包含以下字段:\n\n| json key       | content                  | data type |\n| -------------- | ------------------------ | --------- |\n| name           | 用水单位名称             | string    |\n| address        | 详细地址                 | string    |\n| zipCode        | 邮编                     | string    |\n| lr             | 法定代表人               | string    |\n| projDesc       | 项目概况                 | string    |\n| intakeDesc     | 取水情况                 | string    |\n| waterUsageDesc | 用水情况                 | string    |\n| drainDesc      | 排水情况                 | string    |\n| improveDesc    | 近3年节水改造情况        | string    |\n| improvePlan    | 拟采取节水措施及实施计划 | string    |\n\n\n文字内容:\n石家庄市神兴小学，位于石家庄市东岗路132号，邮政编码050000，自2004年成立以来，已发展成为一所占地8100平方米，建筑面积4800平方米的知名教育机构。学校以其雄厚的师资力量、先进的教育理念和卓越的教育质量，在社会上享有盛誉，被评为石家庄市乃至全国的文明校园，河北省优秀少年大队，全国优秀少先大队，河北省示范家长学校等多项荣誉。法定代表人张立宁担任校长，负责学校全面工作，而用水管理部门由后勤负责人张立峰主管，联系方式为15930123117。学校对外联络电话为031186572038，传真同号。\\n\\n学校高度重视节水工作，建立了节水领导小组，制定了2023年节水工作计划、用水计量和统计制度、用水设备维修巡检制度、节水目标责任和考核制度等一系列节水管理制度。学校供水水源为自来水，供水能力充足，满足学校生活用水需求。学校内部未利用非常规水源，供水压力约为0.25兆帕，通过DN100的供水管道直接供应教学楼、绿化和食堂用水，无需二次加压。\\n\\n学校主要功能用水包括教学楼用水，辅助功能用水涉及校内洗手间，附属功能用水涵盖食堂用水和绿化用水。测试期间，学校总取水量为43.10立方米/天，其中教学楼取水量为18.17立方米/天，耗水量为4.81立方米/天，排水量为13.33立方米/天；绿化混用水量为0.03立方米/天；校内洗手间取水量为10.56立方米/天，耗水量为0.79立方米/天，排水量为9.77立方米/天；食堂取水量为14.37立方米/天，耗水量为4.06立方米/天，排水量为10.31立方米/天；空气冷凝水取水量为0.017立方米/天，耗水量为0.047立方米/天。\\n\\n近三年内，学校实施了节水改造，包括卫生间升级改造，更换为节水型用水器具，以及老旧供水管网改造，有效减少了跑冒滴漏现象，提升了水质。学校计划进一步加强巡检维护，完善二级、三级用水计量系统，制定水资源消耗定额，增强师生节水意识，实现用水的合理化和科学化管理。\\n\\n学校用水情况统计显示，2023年蓄水量为9023立方米，人均用水量为4.34立方米，单位建筑面积用水量为1.88立方米，达标排放率为100%，非常规水资源替代率为0.03%。2024年自来水取水量为8614立方米，人均用水量为3.90立方米，单位建筑面积用水量为1.79立方米，达标排放率同样为100%，非常规水资源替代率为0.04%。\\n\\n学校供水系统计量管理情况良好，一级水表配备率达到100%，二级配备率为66.67%，三级配备率为100%，综合配备率为80%。节水器具配备齐全，包括蹲便器59个，水龙头59个，饮水机60台，墩布池8个，小便池4个，均采用节水型设计，体现了学校在节水工作上的扎实举措和持续努力。\n"}]

    responses = dashscope.Generation.call(
        model="qwen-plus",
        api_key="sk-310c699a8bd343569fa9d7894f43ca32",
        messages=messages,
        stream=True,
        result_format='message',  # 将返回结果格式设置为 message
        top_p=0.0001,
        temperature=0.7,
        enable_search=False,
        enable_thinking=False,
        thinking_budget=4000
    )

    for response in responses:
        if response.status_code == HTTPStatus.OK:
            print(response)
        else:
            print('Request id: %s, Status code: %s, error code: %s, error message: %s' % (
                response.request_id, response.status_code,
                response.code, response.message
            ))


if __name__ == '__main__':
    call_with_messages()