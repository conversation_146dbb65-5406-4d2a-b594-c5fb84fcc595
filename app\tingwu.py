# -*- coding: utf-8 -*-
"""
千义听悟客户端封装类

提供与阿里云千义听悟API交互的功能，包括创建转写任务、查询任务状态等。
"""

import json
import time
import os
from typing import Dict, Any
from alibabacloud_tingwu20230930.client import Client as tingwu20230930Client
from alibabacloud_credentials.client import Client as CredentialClient
from alibabacloud_tea_openapi import models as open_api_models
from alibabacloud_tingwu20230930 import models as tingwu_20230930_models
from alibabacloud_tea_util import models as util_models
from alibabacloud_tea_console.client import Client as ConsoleClient
from alibabacloud_tea_util.client import Client as UtilClient

from .logger import get_logger, log_api_call
from .config_manager import get_config_manager


class TingwuClient:
    """
    千义听悟客户端类

    封装了与阿里云千义听悟API的交互逻辑，提供简单易用的接口。
    """

    def __init__(self, app_key: str = None, environment: str = None):
        """
        初始化千义听悟客户端

        Args:
            app_key (str): 阿里云应用密钥（可选，优先级高于配置文件）
            environment (str): 环境名称（default/development/production）
        """
        self.logger = get_logger()

        # 获取配置管理器
        self.config_manager = get_config_manager(environment)

        # 获取AppKey（参数优先级高于配置文件）
        self.app_key = app_key or self.config_manager.get_app_key()

        # 检查AppKey是否配置
        if not self.app_key:
            raise ValueError("AppKey未配置，请在配置文件中设置app_key或通过参数传入")

        # 获取API配置
        self.api_config = self.config_manager.get_api_config()
        self.logging_config = self.config_manager.get_logging_config()

        # 创建客户端
        self.client = self._create_client()

        # 记录初始化完成（脱敏处理）
        masked_app_key = self.app_key[:8] + '***' if len(self.app_key) > 8 else '***'
        self.logger.info(f"千义听悟客户端初始化完成，AppKey: {masked_app_key}, Environment: {self.config_manager.environment}")

        # 记录配置信息
        config_info = self.config_manager.get_config_info()
        self.logger.info(f"配置来源: {config_info['config_sources']}, 配置文件: {config_info.get('config_file_path', 'None')}")

    def _create_client(self) -> tingwu20230930Client:
        """
        创建阿里云千义听悟客户端

        Returns:
            tingwu20230930Client: 配置好的客户端实例
        """
        try:
            # 设置凭证文件环境变量，使用.ini后缀
            os.environ['ALIBABA_CLOUD_CREDENTIALS_FILE'] = os.path.expanduser('~/.alibabacloud/credentials.ini')

            # 使用凭据初始化客户端
            credential = CredentialClient()

            # 创建配置
            config = open_api_models.Config(
                credential=credential
            )

            # 设置端点 - 千义听悟固定使用北京区域端点
            config.endpoint = 'tingwu.cn-beijing.aliyuncs.com'

            # 设置超时配置
            config.connect_timeout = self.api_config.get('connect_timeout', 30) * 1000  # 转换为毫秒
            config.read_timeout = self.api_config.get('read_timeout', 60) * 1000  # 转换为毫秒

            # 设置连接池配置
            config.max_idle_conns = self.config_manager.get('connection_pool_size', 10)

            self.logger.info(f"创建阿里云客户端，端点: {config.endpoint}, 连接超时: {config.connect_timeout}ms, 读取超时: {config.read_timeout}ms")

            return tingwu20230930Client(config)

        except Exception as e:
            self.logger.error(f"创建阿里云客户端失败: {e}")
            raise Exception(f"无法创建阿里云客户端: {e}")

    def _create_api_params(self) -> open_api_models.Params:
        """
        创建API参数配置

        Returns:
            open_api_models.Params: API参数配置
        """
        # 千义听悟API固定参数 - 与sample.py保持一致
        params = open_api_models.Params(
            action='CreateTask',  # 接口名称
            version='2023-09-30',  # 接口版本 - 固定值
            protocol='HTTPS',  # 接口协议
            method='PUT',  # 接口HTTP方法
            auth_type='AK',
            style='ROA',
            pathname='/openapi/tingwu/v2/tasks',  # 接口PATH - 固定值
            req_body_type='json',  # 接口请求体内容格式
            body_type='json'  # 接口响应体内容格式
        )

        self.logger.debug(f"创建API参数，版本: {params.version}, 路径: {params.pathname}")
        return params

    def create_transcription_task(
        self,
        task_key: str,
        file_url: str,
        source_language: str = "cn",
        sample_rate: int = 16000,
        speaker_count: int = 0,
        output_level: int = 1,
        diarization_enabled: bool = True,
        progressive_callbacks_enabled: bool = True,
        translation_enabled: bool = False,
        auto_chapters_enabled: bool = False,
        meeting_assistance_enabled: bool = False,
        summarization_enabled: bool = False,
        custom_prompt_enabled: bool = True,
        custom_prompt_contents: list = None
    ) -> Dict[str, Any]:
        """创建音频转写任务

        Args:
            task_key (str): 任务唯一标识符
            file_url (str): 音频文件的HTTP访问地址
            source_language (str): 源语言，默认为"cn"（中文）
            sample_rate (int): 音频采样率，默认16000Hz
            speaker_count (int): 说话人数量，0表示自动检测
            output_level (int): 输出级别，1为基础级别
            diarization_enabled (bool): 是否启用说话人分离
            progressive_callbacks_enabled (bool): 是否启用进度回调
            translation_enabled (bool): 是否启用翻译
            auto_chapters_enabled (bool): 是否启用自动章节
            meeting_assistance_enabled (bool): 是否启用会议助手
            summarization_enabled (bool): 是否启用摘要
            custom_prompt_enabled (bool): 是否启用自定义Prompt功能
            custom_prompt_contents (list): 自定义Prompt内容列表，格式为[{'name': 'prompt名称', 'prompt': 'prompt内容'}, ...]
                如果为None或空列表，将使用默认值[{'name': 'concat-dialogs', 'prompt': '把对话内容连接成一整段文本:{Transcription}'}]

        Returns:
            Dict[str, Any]: API响应结果

        Raises:
            Exception: 当API调用失败时抛出异常
        """
        start_time = time.time()

        # 记录API调用参数（隐藏敏感信息）
        api_params = {
            'task_key': task_key,
            'source_language': source_language,
            'sample_rate': sample_rate,
            'speaker_count': speaker_count,
            'output_level': output_level,
            'diarization_enabled': diarization_enabled,
            'progressive_callbacks_enabled': progressive_callbacks_enabled,
            'translation_enabled': translation_enabled,
            'auto_chapters_enabled': auto_chapters_enabled,
            'meeting_assistance_enabled': meeting_assistance_enabled,
            'summarization_enabled': summarization_enabled,
            'custom_prompt_enabled': custom_prompt_enabled
        }

        self.logger.info(f"开始调用千义听悟API创建转写任务，TaskKey: {task_key}")

        try:
            # 构建请求参数 - 使用tingwu_20230930_models模型
            # 说话人分离参数
            parameters_transcription_diarization = tingwu_20230930_models.CreateTaskRequestParametersTranscriptionDiarization(
                speaker_count=speaker_count
            )

            # 转写参数
            parameters_transcription = tingwu_20230930_models.CreateTaskRequestParametersTranscription(
                diarization_enabled=diarization_enabled,
                diarization=parameters_transcription_diarization,
                output_level=output_level
            )

            # 自定义Prompt参数
            parameters_custom_prompt = None

            # 如果custom_prompt_contents为None或空列表，使用默认值
            default_prompt_contents = [{'name': 'concat-dialogs', 'prompt': '把对话内容连接成一整段文本:{Transcription}'}]
            if custom_prompt_contents is None or len(custom_prompt_contents) == 0:
                custom_prompt_contents = default_prompt_contents

            if custom_prompt_enabled and custom_prompt_contents:
                prompt_contents = []
                for content in custom_prompt_contents:
                    if isinstance(content, dict) and 'name' in content and 'prompt' in content:
                        prompt_content = tingwu_20230930_models.CreateTaskRequestParametersCustomPromptContents(
                            name=content['name'],
                            prompt=content['prompt']
                        )
                        prompt_contents.append(prompt_content)

                if prompt_contents:
                    parameters_custom_prompt = tingwu_20230930_models.CreateTaskRequestParametersCustomPrompt(
                        contents=prompt_contents
                    )

            # 参数集合
            parameters = tingwu_20230930_models.CreateTaskRequestParameters(
                transcription=parameters_transcription,
                custom_prompt_enabled=custom_prompt_enabled,
                custom_prompt=parameters_custom_prompt
            )

            # 输入参数
            input_params = tingwu_20230930_models.CreateTaskRequestInput(
                source_language=source_language,
                file_url=file_url,
                task_key=task_key,
                sample_rate=sample_rate,
                progressive_callbacks_enabled=progressive_callbacks_enabled
            )

            # 创建任务请求
            create_task_request = tingwu_20230930_models.CreateTaskRequest(
                type='offline',
                app_key=self.app_key,
                input=input_params,
                parameters=parameters
            )

            self.logger.debug(f"千义听悟API请求参数: {json.dumps(api_params, ensure_ascii=False)}")

            # 运行时选项
            runtime = util_models.RuntimeOptions()

            # 设置重试配置
            retry_config = self.config_manager.get_retry_config()
            if retry_config.get('enabled', True):
                runtime.autoretry = True
                runtime.max_attempts = retry_config.get('max_attempts', 3)
                runtime.backoff = retry_config.get('backoff_multiplier', 2.0)
                self.logger.debug(f"已启用重试机制，最大尝试次数: {runtime.max_attempts}, 退避乘数: {runtime.backoff}")

            # 设置超时配置（单位：毫秒）
            runtime.connect_timeout = self.api_config.get('connect_timeout', 30) * 1000  # 毫秒
            runtime.read_timeout = self.api_config.get('read_timeout', 60) * 1000  # 毫秒

            # 设置连接池大小
            runtime.max_idle_conns = self.config_manager.get('connection_pool_size', 10)

            # 调用API
            self.logger.info(f"正在调用千义听悟API，TaskKey: {task_key}")
            headers = {}
            response = self.client.create_task_with_options(create_task_request, headers, runtime)

            # 计算响应时间
            response_time = time.time() - start_time

            # 解析响应
            if response and hasattr(response, 'body'):
                result = response.body.to_map()

                # 记录成功的API调用
                log_api_call(
                    'tingwu_create_task',
                    api_params,
                    {
                        'task_key': task_key,
                        'status': 'success',
                        'response_time': f'{response_time:.3f}s'
                    }
                )

                self.logger.info(f"千义听悟API调用成功，TaskKey: {task_key}, 耗时: {response_time:.3f}s")

                # 记录响应的关键信息（不记录完整响应以避免日志过大）
                if isinstance(result, dict):
                    response_summary = {
                        'TaskId': result.get('Data', {}).get('TaskId') if 'Data' in result else None,
                        'Code': result.get('Code'),
                        'Message': result.get('Message')
                    }
                    self.logger.debug(f"千义听悟API响应摘要: {json.dumps(response_summary, ensure_ascii=False)}")

                return result
            else:
                error_msg = "API响应格式异常"
                self.logger.error(f"千义听悟API响应格式异常，TaskKey: {task_key}, 响应: {response}")

                # 记录失败的API调用
                log_api_call('tingwu_create_task', api_params, error=error_msg)

                raise Exception(error_msg)

        except Exception as e:
            response_time = time.time() - start_time
            error_msg = f"创建转写任务失败: {str(e)}"

            # 记录失败的API调用
            log_api_call(
                'tingwu_create_task',
                api_params,
                error=f"{error_msg}, 耗时: {response_time:.3f}s"
            )

            self.logger.error(f"千义听悟API调用失败，TaskKey: {task_key}, 错误: {error_msg}, 耗时: {response_time:.3f}s")

            # 输出错误信息和诊断地址
            if hasattr(e, 'message'):
                self.logger.error(f"错误消息: {e.message}")

            if hasattr(e, 'data') and isinstance(e.data, dict) and 'Recommend' in e.data:
                self.logger.error(f"诊断地址: {e.data.get('Recommend')}")

            raise Exception(error_msg)

    def get_task_status(self, task_id: str) -> Dict[str, Any]:
        """
        查询任务状态（预留接口）

        Args:
            task_id (str): 任务ID

        Returns:
            Dict[str, Any]: 任务状态信息

        Note:
            此方法为预留接口，具体实现需要根据千义听悟API文档进行调整
        """
        # TODO: 实现任务状态查询逻辑
        pass

    def get_transcription_result(self, task_id: str) -> Dict[str, Any]:
        """
        获取转写结果（预留接口）

        Args:
            task_id (str): 任务ID

        Returns:
            Dict[str, Any]: 转写结果

        Note:
            此方法为预留接口，具体实现需要根据千义听悟API文档进行调整
        """
        # TODO: 实现转写结果获取逻辑
        pass

    async def create_transcription_task_async(
        self,
        task_key: str,
        file_url: str,
        source_language: str = "cn",
        sample_rate: int = 16000,
        speaker_count: int = 0,
        output_level: int = 1,
        diarization_enabled: bool = True,
        progressive_callbacks_enabled: bool = True,
        translation_enabled: bool = False,
        auto_chapters_enabled: bool = False,
        meeting_assistance_enabled: bool = False,
        summarization_enabled: bool = False,
        custom_prompt_enabled: bool = True,
        custom_prompt_contents: list = None
    ) -> Dict[str, Any]:
        """创建音频转写任务（异步版本）

        Args:
            task_key (str): 任务唯一标识符
            file_url (str): 音频文件的HTTP访问地址
            source_language (str): 源语言，默认为"cn"（中文）
            sample_rate (int): 音频采样率，默认16000Hz
            speaker_count (int): 说话人数量，0表示自动检测
            output_level (int): 输出级别，1为基础级别
            diarization_enabled (bool): 是否启用说话人分离
            progressive_callbacks_enabled (bool): 是否启用进度回调
            translation_enabled (bool): 是否启用翻译
            auto_chapters_enabled (bool): 是否启用自动章节
            meeting_assistance_enabled (bool): 是否启用会议助手
            summarization_enabled (bool): 是否启用摘要
            custom_prompt_enabled (bool): 是否启用自定义Prompt功能
            custom_prompt_contents (list): 自定义Prompt内容列表，格式为[{'name': 'prompt名称', 'prompt': 'prompt内容'}, ...]

        Returns:
            Dict[str, Any]: API响应结果

        Raises:
            Exception: 当API调用失败时抛出异常
        """
        start_time = time.time()

        # 记录API调用参数（隐藏敏感信息）
        api_params = {
            'task_key': task_key,
            'source_language': source_language,
            'sample_rate': sample_rate,
            'speaker_count': speaker_count,
            'output_level': output_level,
            'diarization_enabled': diarization_enabled,
            'progressive_callbacks_enabled': progressive_callbacks_enabled,
            'translation_enabled': translation_enabled,
            'auto_chapters_enabled': auto_chapters_enabled,
            'meeting_assistance_enabled': meeting_assistance_enabled,
            'summarization_enabled': summarization_enabled,
            'custom_prompt_enabled': custom_prompt_enabled
        }

        self.logger.info(f"开始调用千义听悟API创建转写任务（异步），TaskKey: {task_key}")

        try:
            # 构建请求参数 - 使用tingwu_20230930_models模型
            # 说话人分离参数
            parameters_transcription_diarization = tingwu_20230930_models.CreateTaskRequestParametersTranscriptionDiarization(
                speaker_count=speaker_count
            )

            # 转写参数
            parameters_transcription = tingwu_20230930_models.CreateTaskRequestParametersTranscription(
                diarization_enabled=diarization_enabled,
                diarization=parameters_transcription_diarization,
                output_level=output_level
            )

            # 自定义Prompt参数
            parameters_custom_prompt = None

            # 如果custom_prompt_contents为None或空列表，使用默认值
            default_prompt_contents = [{'name': 'concat-dialogs', 'prompt': '把对话内容连接成一整段文本:{Transcription}'}]
            if custom_prompt_contents is None or len(custom_prompt_contents) == 0:
                custom_prompt_contents = default_prompt_contents

            if custom_prompt_enabled and custom_prompt_contents:
                prompt_contents = []
                for content in custom_prompt_contents:
                    if isinstance(content, dict) and 'name' in content and 'prompt' in content:
                        prompt_content = tingwu_20230930_models.CreateTaskRequestParametersCustomPromptContents(
                            name=content['name'],
                            prompt=content['prompt']
                        )
                        prompt_contents.append(prompt_content)

                if prompt_contents:
                    parameters_custom_prompt = tingwu_20230930_models.CreateTaskRequestParametersCustomPrompt(
                        contents=prompt_contents
                    )

            # 参数集合
            parameters = tingwu_20230930_models.CreateTaskRequestParameters(
                transcription=parameters_transcription,
                custom_prompt_enabled=custom_prompt_enabled,
                custom_prompt=parameters_custom_prompt
            )

            # 输入参数
            input_params = tingwu_20230930_models.CreateTaskRequestInput(
                source_language=source_language,
                file_url=file_url,
                task_key=task_key,
                sample_rate=sample_rate,
                progressive_callbacks_enabled=progressive_callbacks_enabled
            )

            # 创建任务请求
            create_task_request = tingwu_20230930_models.CreateTaskRequest(
                type='offline',
                app_key=self.app_key,
                input=input_params,
                parameters=parameters
            )

            self.logger.debug(f"千义听悟API请求参数（异步）: {json.dumps(api_params, ensure_ascii=False)}")

            # 运行时选项
            runtime = util_models.RuntimeOptions()

            # 设置重试配置
            retry_config = self.config_manager.get_retry_config()
            if retry_config.get('enabled', True):
                runtime.autoretry = True
                runtime.max_attempts = retry_config.get('max_attempts', 3)
                runtime.backoff = retry_config.get('backoff_multiplier', 2.0)
                self.logger.debug(f"已启用重试机制，最大尝试次数: {runtime.max_attempts}, 退避乘数: {runtime.backoff}")

            # 设置超时配置（单位：毫秒）
            runtime.connect_timeout = self.api_config.get('connect_timeout', 30) * 1000  # 毫秒
            runtime.read_timeout = self.api_config.get('read_timeout', 60) * 1000  # 毫秒

            # 设置连接池大小
            runtime.max_idle_conns = self.config_manager.get('connection_pool_size', 10)

            # 调用API
            self.logger.info(f"正在调用千义听悟API（异步），TaskKey: {task_key}")
            headers = {}
            response = await self.client.create_task_with_options_async(create_task_request, headers, runtime)

            # 计算响应时间
            response_time = time.time() - start_time

            # 解析响应
            if response and hasattr(response, 'body'):
                result = response.body.to_map()

                # 记录成功的API调用
                log_api_call(
                    'tingwu_create_task_async',
                    api_params,
                    {
                        'task_key': task_key,
                        'status': 'success',
                        'response_time': f'{response_time:.3f}s'
                    }
                )

                self.logger.info(f"千义听悟API调用成功（异步），TaskKey: {task_key}, 耗时: {response_time:.3f}s")

                # 记录响应的关键信息（不记录完整响应以避免日志过大）
                if isinstance(result, dict):
                    response_summary = {
                        'TaskId': result.get('Data', {}).get('TaskId') if 'Data' in result else None,
                        'Code': result.get('Code'),
                        'Message': result.get('Message')
                    }
                    self.logger.debug(f"千义听悟API响应摘要（异步）: {json.dumps(response_summary, ensure_ascii=False)}")

                return result
            else:
                error_msg = "API响应格式异常（异步）"
                self.logger.error(f"千义听悟API响应格式异常（异步），TaskKey: {task_key}, 响应: {response}")

                # 记录失败的API调用
                log_api_call('tingwu_create_task_async', api_params, error=error_msg)

                raise Exception(error_msg)

        except Exception as e:
            response_time = time.time() - start_time
            error_msg = f"创建转写任务失败（异步）: {str(e)}"

            # 记录失败的API调用
            log_api_call(
                'tingwu_create_task_async',
                api_params,
                error=f"{error_msg}, 耗时: {response_time:.3f}s"
            )

            self.logger.error(f"千义听悟API调用失败（异步），TaskKey: {task_key}, 错误: {error_msg}, 耗时: {response_time:.3f}s")

            # 输出错误信息和诊断地址
            if hasattr(e, 'message'):
                self.logger.error(f"错误消息（异步）: {e.message}")

            if hasattr(e, 'data') and isinstance(e.data, dict) and 'Recommend' in e.data:
                self.logger.error(f"诊断地址（异步）: {e.data.get('Recommend')}")

            raise Exception(error_msg)