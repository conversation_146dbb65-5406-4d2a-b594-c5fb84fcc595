# -*- coding: utf-8 -*-
"""
数据验证模块

使用Pydantic进行请求数据验证，提供类型安全和自动验证功能。
本模块包含所有API请求的数据模型定义和验证逻辑。
"""

import re
from typing import Optional, Literal, Annotated
from pydantic import BaseModel, Field, field_validator, HttpUrl
from pydantic.types import conint


class TingwuTaskRequest(BaseModel):
    """
    千义听悟转写任务请求数据模型

    用于验证创建音频转写任务的请求参数，包含必需字段和可选配置参数。
    """

    # 必需字段
    TaskKey: Annotated[str, Field(
        min_length=1,
        max_length=100,
        pattern=r'^[a-zA-Z0-9_-]+$',
        description="任务唯一标识符，只能包含字母、数字、下划线和连字符，长度1-100字符",
        examples=["audio_task_20240101_001"]
    )]

    fileUrl: HttpUrl = Field(
        ...,
        description="音频文件的HTTP或HTTPS URL地址",
        example="https://example.com/audio/sample.mp3"
    )

    # 可选整数字段
    sampleRate: Optional[conint(ge=8000, le=48000)] = Field(
        None,
        description="音频采样率，范围8000-48000Hz",
        example=16000
    )

    speakerCount: Optional[conint(ge=0, le=20)] = Field(
        None,
        description="说话人数量，范围0-20人，0表示自动检测",
        example=2
    )

    outputLevel: Optional[conint(ge=1, le=3)] = Field(
        None,
        description="输出详细级别，1=基础，2=标准，3=详细",
        example=2
    )

    # 可选布尔字段
    diarizationEnabled: Optional[bool] = Field(
        None,
        description="是否启用说话人分离功能",
        example=True
    )

    progressiveCallbacksEnabled: Optional[bool] = Field(
        None,
        description="是否启用渐进式回调通知",
        example=False
    )

    translationEnabled: Optional[bool] = Field(
        None,
        description="是否启用翻译功能",
        example=False
    )

    autoChaptersEnabled: Optional[bool] = Field(
        None,
        description="是否启用自动章节分割",
        example=True
    )

    meetingAssistanceEnabled: Optional[bool] = Field(
        None,
        description="是否启用会议辅助功能",
        example=False
    )

    summarizationEnabled: Optional[bool] = Field(
        None,
        description="是否启用内容摘要功能",
        example=True
    )

    # 源语言字段
    sourceLanguage: Optional[Literal[
        'cn', 'en', 'ja', 'ko', 'es', 'fr', 'de', 'it', 'pt', 'ru'
    ]] = Field(
        None,
        description="源语言代码，支持中文(cn)、英文(en)、日文(ja)、韩文(ko)、西班牙文(es)、法文(fr)、德文(de)、意大利文(it)、葡萄牙文(pt)、俄文(ru)",
        example="cn"
    )

    @field_validator('TaskKey')
    @classmethod
    def validate_task_key(cls, v):
        """
        验证TaskKey格式

        Args:
            v: TaskKey值

        Returns:
            str: 验证通过的TaskKey

        Raises:
            ValueError: 当TaskKey格式不符合要求时
        """
        if not v:
            raise ValueError('TaskKey不能为空')

        if not re.match(r'^[a-zA-Z0-9_-]+$', v):
            raise ValueError('TaskKey只能包含字母、数字、下划线和连字符')

        if len(v) > 100:
            raise ValueError('TaskKey长度不能超过100个字符')

        return v

    @field_validator('fileUrl')
    @classmethod
    def validate_file_url(cls, v):
        """
        验证文件URL格式

        Args:
            v: 文件URL

        Returns:
            str: 验证通过的URL

        Raises:
            ValueError: 当URL格式不符合要求时
        """
        if not v:
            raise ValueError('fileUrl不能为空')

        # 转换为字符串进行进一步验证
        url_str = str(v)

        # 检查是否为HTTP或HTTPS协议
        if not url_str.startswith(('http://', 'https://')):
            raise ValueError('fileUrl必须是HTTP或HTTPS协议的URL')

        return v

    class Config:
        """Pydantic配置类"""
        # 允许使用字段别名
        allow_population_by_field_name = True
        # 验证赋值
        validate_assignment = True
        # 使用枚举值而不是枚举名称
        use_enum_values = True
        # 示例数据
        schema_extra = {
            "example": {
                "TaskKey": "audio_task_20240101_001",
                "fileUrl": "https://example.com/audio/sample.mp3",
                "sampleRate": 16000,
                "speakerCount": 2,
                "outputLevel": 2,
                "diarizationEnabled": True,
                "progressiveCallbacksEnabled": False,
                "translationEnabled": False,
                "autoChaptersEnabled": True,
                "meetingAssistanceEnabled": False,
                "summarizationEnabled": True,
                "sourceLanguage": "cn"
            }
        }


class ChatRequest(BaseModel):
    """
    大模型聊天请求数据模型

    用于验证大模型文本交互请求的参数。
    """

    Model: Optional[str] = Field(
        "qwen-plus",
        description="使用的大模型名称",
        example="qwen-plus"
    )

    SysContent: str = Field(
        ...,
        min_length=1,
        max_length=2000,
        description="系统角色的内容，定义AI助手的行为和角色",
        example="你是一个专业的AI助手"
    )

    UserContent: str = Field(
        ...,
        min_length=1,
        max_length=4000,
        description="用户输入的内容",
        example="请介绍一下人工智能的发展历程"
    )

    @field_validator('SysContent', 'UserContent')
    @classmethod
    def validate_content_not_empty(cls, v):
        """
        验证内容不为空

        Args:
            v: 内容字符串

        Returns:
            str: 验证通过的内容

        Raises:
            ValueError: 当内容为空时
        """
        if not v or not v.strip():
            raise ValueError('内容不能为空')
        return v.strip()

    class Config:
        """Pydantic配置类"""
        # 允许使用字段别名
        allow_population_by_field_name = True
        # 验证赋值
        validate_assignment = True
        # 示例数据
        schema_extra = {
            "example": {
                "Model": "qwen-plus",
                "SysContent": "你是一个专业的AI助手",
                "UserContent": "请介绍一下人工智能的发展历程"
            }
        }


def validate_tingwu_request(data: dict) -> TingwuTaskRequest:
    """
    验证千义听悟请求数据

    Args:
        data (dict): 请求数据字典

    Returns:
        TingwuTaskRequest: 验证通过的请求模型实例

    Raises:
        ValidationError: 当数据验证失败时
    """
    return TingwuTaskRequest(**data)


def validate_chat_request(data: dict) -> ChatRequest:
    """
    验证聊天请求数据

    Args:
        data (dict): 请求数据字典

    Returns:
        ChatRequest: 验证通过的请求模型实例

    Raises:
        ValidationError: 当数据验证失败时
    """
    return ChatRequest(**data)
