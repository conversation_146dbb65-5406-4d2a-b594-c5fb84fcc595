#!/bin/bash

# AIHub 服务停止脚本
# 功能：停止FastAPI + Uvicorn服务

# 设置脚本目录为工作目录
cd "$(dirname "$0")"

# PID文件路径
PID_FILE="./logs/aihub.pid"
LOG_FILE="./logs/aihub.log"

echo "正在停止 AIHub 服务..."


# 停止所有相关进程
pkill -f "start.sh"
pkill -f "python -m app.prod"

# 如果PID文件存在，尝试杀死主进程
if [ -f "$PID_FILE" ]; then
    PID=$(cat "$PID_FILE")
    if kill -0 "$PID" 2>/dev/null; then
        kill "$PID"
        echo "已发送停止信号给进程 $PID"
    fi
    rm -f "$PID_FILE"
fi

echo "[$(date '+%Y-%m-%d %H:%M:%S')] 服务已停止" >> "$LOG_FILE"
echo "AIHub 服务已停止"
