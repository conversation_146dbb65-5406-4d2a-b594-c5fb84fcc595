# AI Hub服务器 Docker镜像
# 基于Python 3.10官方镜像

FROM python:3.10-slim

# 设置维护者信息
LABEL maintainer="Assistant"
LABEL description="AI Hub Server"
LABEL version="1.0.0"

# 设置环境变量
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    DEBIAN_FRONTEND=noninteractive

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    curl \
    wget \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# 创建非root用户
RUN groupadd -r aihub && useradd -r -g aihub aihub

# 复制requirements文件并安装Python依赖
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY app/ ./app/
COPY setup.py .
COPY README.md .

# 创建日志目录
RUN mkdir -p /app/logs && chown -R aihub:aihub /app

# 切换到非root用户
USER aihub

# 暴露端口
EXPOSE 5579

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:5579/health || exit 1

# 启动命令
CMD ["python", "-m", "app.app"]