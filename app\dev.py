# -*- coding: utf-8 -*-
"""
千义听悟音频转写HTTP服务器开发运行脚本

用于在开发环境中启动服务器。
"""

import sys
import uvicorn
from .app import app, check_python_version, check_dependencies, check_credentials, logger
from .config_manager import get_config_manager


def main():
    """主函数，用于启动开发服务器"""
    # 环境检查
    if not check_python_version():
        sys.exit(1)

    if not check_dependencies():
        sys.exit(1)

    # 检查凭证（仅警告，不退出）
    check_credentials()

    # 获取配置管理器和服务器配置
    config_manager = get_config_manager()
    host = config_manager.get('host')
    port = config_manager.get('port')
    debug = config_manager.get('debug')

    # 开发环境运行
    logger.info(f"启动AIHUB开发服务器，监听地址: {host}:{port}")

    # 使用Uvicorn启动FastAPI应用
    uvicorn.run(
        "app.app:app",
        host=host,
        port=port,
        reload=debug,
        log_level="info" if not debug else "debug"
    )


if __name__ == '__main__':
    main()
