#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试运行脚本

用于运行Pydantic验证器的测试，验证数据验证功能的正确性。
"""

import sys
import os
import subprocess
from pathlib import Path


def check_pytest_installed():
    """检查pytest是否已安装"""
    try:
        import pytest
        print(f"✓ pytest已安装，版本: {pytest.__version__}")
        return True
    except ImportError:
        print("✗ pytest未安装")
        print("请运行以下命令安装pytest:")
        print("pip install pytest")
        return False


def run_validation_tests():
    """运行验证器测试"""
    print("=" * 60)
    print("运行Pydantic验证器测试")
    print("=" * 60)
    
    # 检查pytest
    if not check_pytest_installed():
        return False
    
    # 确保tests目录存在
    tests_dir = Path("tests")
    if not tests_dir.exists():
        print(f"✗ 测试目录不存在: {tests_dir}")
        return False
    
    # 运行测试
    test_file = tests_dir / "test_validators.py"
    if not test_file.exists():
        print(f"✗ 测试文件不存在: {test_file}")
        return False
    
    print(f"运行测试文件: {test_file}")
    print("-" * 60)
    
    try:
        # 运行pytest
        result = subprocess.run([
            sys.executable, "-m", "pytest", 
            str(test_file), 
            "-v",  # 详细输出
            "--tb=short",  # 简短的错误回溯
            "--color=yes"  # 彩色输出
        ], capture_output=False, text=True)
        
        if result.returncode == 0:
            print("-" * 60)
            print("✓ 所有测试通过！")
            return True
        else:
            print("-" * 60)
            print("✗ 部分测试失败")
            return False
            
    except Exception as e:
        print(f"✗ 运行测试时出错: {e}")
        return False


def run_manual_validation_test():
    """运行手动验证测试"""
    print("\n" + "=" * 60)
    print("运行手动验证测试")
    print("=" * 60)
    
    try:
        # 导入验证器
        from app.validators import validate_tingwu_request, validate_chat_request
        from pydantic import ValidationError
        
        print("1. 测试千义听悟请求验证...")
        
        # 测试有效请求
        try:
            valid_data = {
                "TaskKey": "test_task_123",
                "fileUrl": "https://example.com/audio.mp3",
                "sampleRate": 16000,
                "sourceLanguage": "cn"
            }
            request = validate_tingwu_request(valid_data)
            print(f"   ✓ 有效请求验证通过: TaskKey={request.TaskKey}")
        except Exception as e:
            print(f"   ✗ 有效请求验证失败: {e}")
            return False
        
        # 测试无效请求
        try:
            invalid_data = {
                "TaskKey": "invalid@key",  # 包含非法字符
                "fileUrl": "invalid-url"   # 无效URL
            }
            validate_tingwu_request(invalid_data)
            print("   ✗ 无效请求应该验证失败但却通过了")
            return False
        except ValidationError:
            print("   ✓ 无效请求正确被拒绝")
        except Exception as e:
            print(f"   ✗ 无效请求验证出现意外错误: {e}")
            return False
        
        print("\n2. 测试聊天请求验证...")
        
        # 测试有效聊天请求
        try:
            valid_chat_data = {
                "SysContent": "你是一个AI助手",
                "UserContent": "你好，请介绍一下自己"
            }
            chat_request = validate_chat_request(valid_chat_data)
            print(f"   ✓ 有效聊天请求验证通过: Model={chat_request.Model}")
        except Exception as e:
            print(f"   ✗ 有效聊天请求验证失败: {e}")
            return False
        
        # 测试无效聊天请求
        try:
            invalid_chat_data = {
                "SysContent": "",  # 空内容
                "UserContent": "你好"
            }
            validate_chat_request(invalid_chat_data)
            print("   ✗ 无效聊天请求应该验证失败但却通过了")
            return False
        except ValidationError:
            print("   ✓ 无效聊天请求正确被拒绝")
        except Exception as e:
            print(f"   ✗ 无效聊天请求验证出现意外错误: {e}")
            return False
        
        print("\n✓ 所有手动验证测试通过！")
        return True
        
    except ImportError as e:
        print(f"✗ 导入验证器模块失败: {e}")
        print("请确保app/validators.py文件存在且语法正确")
        return False
    except Exception as e:
        print(f"✗ 手动验证测试出错: {e}")
        return False


def check_code_style():
    """检查代码风格（如果安装了flake8）"""
    print("\n" + "=" * 60)
    print("检查代码风格")
    print("=" * 60)
    
    try:
        import flake8
        print(f"✓ flake8已安装，版本: {flake8.__version__}")
        
        # 检查主要文件
        files_to_check = [
            "app/validators.py",
            "app/utils.py",
            "tests/test_validators.py"
        ]
        
        for file_path in files_to_check:
            if Path(file_path).exists():
                print(f"检查文件: {file_path}")
                result = subprocess.run([
                    sys.executable, "-m", "flake8", 
                    file_path,
                    "--max-line-length=120",  # 允许更长的行
                    "--ignore=E501,W503"      # 忽略某些规则
                ], capture_output=True, text=True)
                
                if result.returncode == 0:
                    print(f"   ✓ {file_path} 代码风格检查通过")
                else:
                    print(f"   ⚠ {file_path} 代码风格问题:")
                    print(f"     {result.stdout}")
            else:
                print(f"   ⚠ 文件不存在: {file_path}")
        
        return True
        
    except ImportError:
        print("⚠ flake8未安装，跳过代码风格检查")
        print("如需检查代码风格，请运行: pip install flake8")
        return True


def main():
    """主函数"""
    print("AI Hub服务器 - Pydantic验证器测试")
    print("=" * 60)
    
    # 设置Python路径
    current_dir = Path(__file__).parent
    sys.path.insert(0, str(current_dir))
    
    success = True
    
    # 运行pytest测试
    if not run_validation_tests():
        success = False
    
    # 运行手动验证测试
    if not run_manual_validation_test():
        success = False
    
    # 检查代码风格
    if not check_code_style():
        success = False
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 所有测试和检查都通过了！")
        print("Pydantic验证器工作正常，可以安全使用。")
    else:
        print("❌ 部分测试或检查失败")
        print("请检查上述错误信息并修复问题。")
    print("=" * 60)
    
    return 0 if success else 1


if __name__ == "__main__":
    sys.exit(main())
