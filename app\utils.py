# -*- coding: utf-8 -*-
"""
工具函数模块

提供请求验证、响应格式化等辅助功能。
"""

import re
from typing import Dict, Any, Optional, Tuple
from fastapi import HTTPException
from fastapi.responses import JSONResponse


def validate_request_data(data: Dict[str, Any]) -> Optional[str]:
    """
    验证请求数据的有效性

    Args:
        data (Dict[str, Any]): 请求数据

    Returns:
        Optional[str]: 如果验证失败返回错误信息，否则返回None
    """
    # 检查必需字段
    required_fields = ['TaskKey', 'fileUrl']
    for field in required_fields:
        if field not in data:
            return f"缺少必需字段: {field}"
        if not data[field] or not isinstance(data[field], str):
            return f"字段 {field} 不能为空且必须是字符串类型"

    # 验证TaskKey格式（只允许字母、数字、下划线、连字符）
    task_key = data['TaskKey']
    if not re.match(r'^[a-zA-Z0-9_-]+$', task_key):
        return "TaskKey只能包含字母、数字、下划线和连字符"

    if len(task_key) > 100:
        return "TaskKey长度不能超过100个字符"

    # 验证fileUrl格式
    file_url = data['fileUrl']
    if not is_valid_url(file_url):
        return "fileUrl格式无效，必须是有效的HTTP或HTTPS URL"

    # 验证可选参数类型
    optional_int_fields = {
        'sampleRate': (8000, 48000),  # 采样率范围
        'speakerCount': (0, 20),      # 说话人数量范围
        'outputLevel': (1, 3)         # 输出级别范围
    }

    for field, (min_val, max_val) in optional_int_fields.items():
        if field in data:
            value = data[field]
            if not isinstance(value, int):
                return f"字段 {field} 必须是整数类型"
            if not (min_val <= value <= max_val):
                return f"字段 {field} 的值必须在 {min_val} 到 {max_val} 之间"

    # 验证可选布尔字段
    optional_bool_fields = [
        'diarizationEnabled',
        'progressiveCallbacksEnabled',
        'translationEnabled',
        'autoChaptersEnabled',
        'meetingAssistanceEnabled',
        'summarizationEnabled'
    ]

    for field in optional_bool_fields:
        if field in data and not isinstance(data[field], bool):
            return f"字段 {field} 必须是布尔类型"

    # 验证源语言
    if 'sourceLanguage' in data:
        source_language = data['sourceLanguage']
        if not isinstance(source_language, str):
            return "sourceLanguage必须是字符串类型"

        # 支持的语言列表（可根据实际API支持情况调整）
        supported_languages = ['cn', 'en', 'ja', 'ko', 'es', 'fr', 'de', 'it', 'pt', 'ru']
        if source_language not in supported_languages:
            return f"不支持的源语言: {source_language}，支持的语言: {', '.join(supported_languages)}"

    return None


def is_valid_url(url: str) -> bool:
    """
    验证URL格式是否有效

    Args:
        url (str): 要验证的URL

    Returns:
        bool: URL是否有效
    """
    # 简单的URL格式验证
    url_pattern = re.compile(
        r'^https?://'  # http:// 或 https://
        r'(?:(?:[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?\.)+'  # 域名
        r'(?:[A-Z]{2,6}\.?|[A-Z0-9-]{2,}\.?)|'  # 顶级域名
        r'localhost|'  # localhost
        r'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})'  # IP地址
        r'(?::\d+)?'  # 可选端口
        r'(?:/?|[/?]\S+)$', re.IGNORECASE)  # 路径

    return bool(url_pattern.match(url))


def create_error_response(message: str, status_code: int = 400) -> JSONResponse:
    """
    创建错误响应

    Args:
        message (str): 错误信息
        status_code (int): HTTP状态码，默认400

    Returns:
        JSONResponse: FastAPI响应对象
    """
    response = {
        "Code": str(status_code),
        "Message": message,
        "Data": None,
        "RequestId": None
    }
    return JSONResponse(content=response, status_code=status_code)


def create_success_response(data: Dict[str, Any], message: str = "success") -> Dict[str, Any]:
    """
    创建成功响应

    Args:
        data (Dict[str, Any]): 响应数据
        message (str): 成功信息，默认"success"

    Returns:
        Dict[str, Any]: 响应数据字典
    """
    response = {
        "Code": "0",
        "Message": message,
        "Data": data,
        "RequestId": data.get("RequestId") if isinstance(data, dict) else None
    }
    return response


def format_file_size(size_bytes: int) -> str:
    """
    格式化文件大小显示

    Args:
        size_bytes (int): 文件大小（字节）

    Returns:
        str: 格式化后的文件大小字符串
    """
    if size_bytes == 0:
        return "0B"

    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1

    return f"{size_bytes:.1f}{size_names[i]}"


def sanitize_filename(filename: str) -> str:
    """
    清理文件名，移除不安全字符

    Args:
        filename (str): 原始文件名

    Returns:
        str: 清理后的文件名
    """
    # 移除路径分隔符和其他不安全字符
    unsafe_chars = ['/', '\\', ':', '*', '?', '"', '<', '>', '|']
    for char in unsafe_chars:
        filename = filename.replace(char, '_')

    # 限制文件名长度
    if len(filename) > 255:
        name, ext = filename.rsplit('.', 1) if '.' in filename else (filename, '')
        max_name_length = 255 - len(ext) - 1 if ext else 255
        filename = name[:max_name_length] + ('.' + ext if ext else '')

    return filename


def extract_audio_info_from_url(url: str) -> Dict[str, Any]:
    """
    从URL中提取音频文件信息

    Args:
        url (str): 音频文件URL

    Returns:
        Dict[str, Any]: 音频文件信息
    """
    import os
    from urllib.parse import urlparse, unquote

    parsed_url = urlparse(url)
    filename = os.path.basename(unquote(parsed_url.path))

    # 提取文件扩展名
    name, ext = os.path.splitext(filename) if '.' in filename else (filename, '')

    # 支持的音频格式
    audio_formats = {
        '.mp3': 'MP3',
        '.wav': 'WAV',
        '.m4a': 'M4A',
        '.aac': 'AAC',
        '.flac': 'FLAC',
        '.ogg': 'OGG',
        '.wma': 'WMA',
        '.mp4': 'MP4',  # 视频文件也可能包含音频
        '.avi': 'AVI',
        '.mov': 'MOV',
        '.mkv': 'MKV'
    }

    file_format = audio_formats.get(ext.lower(), 'Unknown')

    return {
        'filename': filename,
        'name': name,
        'extension': ext,
        'format': file_format,
        'url': url,
        'domain': parsed_url.netloc
    }
