# -*- coding: utf-8 -*-
"""
工具函数模块

提供响应格式化、文件处理等辅助功能。
数据验证功能已迁移到validators.py模块，使用Pydantic进行类型安全的验证。
"""

from typing import Dict, Any
from fastapi.responses import JSONResponse


def create_error_response(message: str, status_code: int = 400) -> JSONResponse:
    """
    创建标准化的错误响应

    用于生成统一格式的API错误响应，包含错误码、错误信息等标准字段。

    Args:
        message (str): 错误信息描述
        status_code (int): HTTP状态码，默认为400（客户端错误）

    Returns:
        JSONResponse: FastAPI JSON响应对象，包含标准化的错误信息格式

    Example:
        >>> error_response = create_error_response("参数验证失败", 400)
        >>> # 返回: {"Code": "400", "Message": "参数验证失败", "Data": None, "RequestId": None}
    """
    response = {
        "Code": str(status_code),
        "Message": message,
        "Data": None,
        "RequestId": None
    }
    return JSONResponse(content=response, status_code=status_code)


def create_success_response(data: Dict[str, Any], message: str = "success") -> Dict[str, Any]:
    """
    创建标准化的成功响应

    用于生成统一格式的API成功响应，包含成功码、数据内容等标准字段。

    Args:
        data (Dict[str, Any]): 响应数据内容，通常包含API调用的结果
        message (str): 成功信息描述，默认为"success"

    Returns:
        Dict[str, Any]: 标准化的响应数据字典

    Example:
        >>> success_data = {"TaskId": "12345", "Status": "Created"}
        >>> response = create_success_response(success_data, "任务创建成功")
        >>> # 返回: {"Code": "0", "Message": "任务创建成功", "Data": {...}, "RequestId": "12345"}
    """
    response = {
        "Code": "0",
        "Message": message,
        "Data": data,
        "RequestId": data.get("RequestId") if isinstance(data, dict) else None
    }
    return response


def format_file_size(size_bytes: int) -> str:
    """
    格式化文件大小显示

    将字节数转换为人类可读的文件大小格式（B, KB, MB, GB, TB）。

    Args:
        size_bytes (int): 文件大小（以字节为单位）

    Returns:
        str: 格式化后的文件大小字符串，保留一位小数

    Example:
        >>> format_file_size(1024)
        '1.0KB'
        >>> format_file_size(1536)
        '1.5KB'
        >>> format_file_size(0)
        '0B'
    """
    if size_bytes == 0:
        return "0B"

    # 定义文件大小单位
    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0

    # 循环除以1024直到找到合适的单位
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1

    return f"{size_bytes:.1f}{size_names[i]}"


def sanitize_filename(filename: str) -> str:
    """
    清理文件名，移除不安全字符

    移除文件名中的路径分隔符和其他可能导致安全问题的字符，
    确保文件名可以安全地在文件系统中使用。

    Args:
        filename (str): 原始文件名

    Returns:
        str: 清理后的安全文件名

    Example:
        >>> sanitize_filename("test/file<name>.txt")
        'test_file_name_.txt'
        >>> sanitize_filename("very_long_filename" * 20 + ".txt")
        # 返回截断后的文件名，保持在255字符以内
    """
    # 定义不安全的字符列表
    unsafe_chars = ['/', '\\', ':', '*', '?', '"', '<', '>', '|']

    # 将不安全字符替换为下划线
    for char in unsafe_chars:
        filename = filename.replace(char, '_')

    # 限制文件名长度（大多数文件系统的限制是255字符）
    if len(filename) > 255:
        # 尝试分离文件名和扩展名
        if '.' in filename:
            name, ext = filename.rsplit('.', 1)
            # 计算允许的文件名长度（减去扩展名和点的长度）
            max_name_length = 255 - len(ext) - 1
            filename = name[:max_name_length] + '.' + ext
        else:
            # 没有扩展名的情况
            filename = filename[:255]

    return filename


def extract_audio_info_from_url(url: str) -> Dict[str, Any]:
    """
    从URL中提取音频文件信息

    解析音频文件URL，提取文件名、格式、域名等信息，
    支持多种常见的音频和视频格式。

    Args:
        url (str): 音频文件的完整URL地址

    Returns:
        Dict[str, Any]: 包含音频文件详细信息的字典，包含以下字段：
            - filename: 完整文件名
            - name: 不含扩展名的文件名
            - extension: 文件扩展名
            - format: 文件格式类型
            - url: 原始URL
            - domain: 域名

    Example:
        >>> info = extract_audio_info_from_url("https://example.com/audio/sample.mp3")
        >>> print(info)
        {
            'filename': 'sample.mp3',
            'name': 'sample',
            'extension': '.mp3',
            'format': 'MP3',
            'url': 'https://example.com/audio/sample.mp3',
            'domain': 'example.com'
        }
    """
    import os
    from urllib.parse import urlparse, unquote

    # 解析URL
    parsed_url = urlparse(url)

    # 从URL路径中提取文件名，并进行URL解码
    filename = os.path.basename(unquote(parsed_url.path))

    # 分离文件名和扩展名
    if '.' in filename:
        name, ext = os.path.splitext(filename)
    else:
        name, ext = filename, ''

    # 支持的音频和视频格式映射表
    audio_formats = {
        # 音频格式
        '.mp3': 'MP3',
        '.wav': 'WAV',
        '.m4a': 'M4A',
        '.aac': 'AAC',
        '.flac': 'FLAC',
        '.ogg': 'OGG',
        '.wma': 'WMA',
        '.opus': 'OPUS',
        '.aiff': 'AIFF',
        '.au': 'AU',

        # 视频格式（通常包含音频轨道）
        '.mp4': 'MP4',
        '.avi': 'AVI',
        '.mov': 'MOV',
        '.mkv': 'MKV',
        '.wmv': 'WMV',
        '.flv': 'FLV',
        '.webm': 'WEBM',
        '.3gp': '3GP'
    }

    # 获取文件格式，不区分大小写
    file_format = audio_formats.get(ext.lower(), 'Unknown')

    return {
        'filename': filename,
        'name': name,
        'extension': ext,
        'format': file_format,
        'url': url,
        'domain': parsed_url.netloc
    }
