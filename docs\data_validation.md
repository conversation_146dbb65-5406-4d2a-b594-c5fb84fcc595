# 数据验证机制说明

## 概述

AI Hub服务器使用 **Pydantic** 作为数据验证框架，提供类型安全、高性能的请求数据验证。Pydantic是一个现代的Python数据验证库，广泛应用于FastAPI等现代Web框架中。

## 验证优势

### 🔒 类型安全
- 自动进行数据类型检查和转换
- 防止类型错误导致的运行时异常
- 支持复杂的数据类型验证

### 📝 详细错误信息
- 提供具体的字段验证错误信息
- 支持多语言错误消息
- 便于客户端进行错误处理

### 🚀 高性能
- 基于Python类型注解的高效验证
- 编译时优化，运行时性能优异
- 支持异步验证

### 📚 自动文档生成
- 与FastAPI集成，自动生成OpenAPI文档
- 支持Swagger UI交互式文档
- 提供完整的API规范

## 验证模型

### 千义听悟请求验证 (TingwuTaskRequest)

```python
class TingwuTaskRequest(BaseModel):
    # 必需字段
    TaskKey: constr(regex=r'^[a-zA-Z0-9_-]+$', min_length=1, max_length=100)
    fileUrl: HttpUrl
    
    # 可选字段
    sampleRate: Optional[conint(ge=8000, le=48000)] = None
    speakerCount: Optional[conint(ge=0, le=20)] = None
    outputLevel: Optional[conint(ge=1, le=3)] = None
    
    # 布尔字段
    diarizationEnabled: Optional[bool] = None
    progressiveCallbacksEnabled: Optional[bool] = None
    translationEnabled: Optional[bool] = None
    autoChaptersEnabled: Optional[bool] = None
    meetingAssistanceEnabled: Optional[bool] = None
    summarizationEnabled: Optional[bool] = None
    
    # 枚举字段
    sourceLanguage: Optional[Literal['cn', 'en', 'ja', 'ko', 'es', 'fr', 'de', 'it', 'pt', 'ru']] = None
```

### 聊天请求验证 (ChatRequest)

```python
class ChatRequest(BaseModel):
    Model: Optional[str] = "qwen-plus"
    SysContent: str = Field(..., min_length=1, max_length=2000)
    UserContent: str = Field(..., min_length=1, max_length=4000)
```

## 验证规则详解

### 字段类型约束

| 约束类型 | 说明 | 示例 |
|----------|------|------|
| `constr` | 字符串约束 | `constr(regex=r'^[a-zA-Z0-9_-]+$', min_length=1, max_length=100)` |
| `conint` | 整数约束 | `conint(ge=8000, le=48000)` |
| `HttpUrl` | URL格式验证 | 自动验证HTTP/HTTPS URL格式 |
| `Literal` | 枚举值约束 | `Literal['cn', 'en', 'ja']` |
| `Field` | 字段元数据 | `Field(..., min_length=1, description="字段描述")` |

### 自定义验证器

服务器还包含自定义验证器，提供更精确的业务逻辑验证：

```python
@validator('TaskKey')
def validate_task_key(cls, v):
    if not v:
        raise ValueError('TaskKey不能为空')
    if not re.match(r'^[a-zA-Z0-9_-]+$', v):
        raise ValueError('TaskKey只能包含字母、数字、下划线和连字符')
    if len(v) > 100:
        raise ValueError('TaskKey长度不能超过100个字符')
    return v
```

## 错误处理

### 错误响应格式

当验证失败时，服务器返回标准化的错误响应：

```json
{
  "Code": "400",
  "Message": "请求参数验证失败: 具体错误信息",
  "Data": null,
  "RequestId": null
}
```

### 常见验证错误

#### 1. 必需字段缺失
```json
{
  "Code": "400",
  "Message": "请求参数验证失败: TaskKey: field required; fileUrl: field required",
  "Data": null,
  "RequestId": null
}
```

#### 2. 字段格式错误
```json
{
  "Code": "400",
  "Message": "请求参数验证失败: TaskKey: string does not match expected pattern",
  "Data": null,
  "RequestId": null
}
```

#### 3. 数值范围错误
```json
{
  "Code": "400",
  "Message": "请求参数验证失败: sampleRate: ensure this value is greater than or equal to 8000",
  "Data": null,
  "RequestId": null
}
```

#### 4. 字符串长度错误
```json
{
  "Code": "400",
  "Message": "请求参数验证失败: UserContent: ensure this value has at least 1 characters",
  "Data": null,
  "RequestId": null
}
```

#### 5. URL格式错误
```json
{
  "Code": "400",
  "Message": "请求参数验证失败: fileUrl: invalid or missing URL scheme",
  "Data": null,
  "RequestId": null
}
```

## 最佳实践

### 客户端开发建议

1. **错误处理**: 始终检查响应状态码，妥善处理验证错误
2. **数据预验证**: 在发送请求前进行基本的客户端验证
3. **错误展示**: 将验证错误信息友好地展示给用户
4. **重试机制**: 对于网络错误实施重试，对于验证错误不要重试

### 示例代码

#### Python客户端
```python
import requests
import json

def call_tingwu_api(task_key, file_url, **kwargs):
    url = "http://localhost:5579/ai/aliyun/voice/tingwu/new"
    data = {
        "TaskKey": task_key,
        "fileUrl": file_url,
        **kwargs
    }
    
    try:
        response = requests.post(url, json=data)
        response.raise_for_status()
        return response.json()
    except requests.exceptions.HTTPError as e:
        if response.status_code == 400:
            error_info = response.json()
            print(f"验证错误: {error_info['Message']}")
        else:
            print(f"HTTP错误: {e}")
    except Exception as e:
        print(f"请求错误: {e}")
```

#### JavaScript客户端
```javascript
async function callTingwuAPI(taskKey, fileUrl, options = {}) {
    const url = 'http://localhost:5579/ai/aliyun/voice/tingwu/new';
    const data = {
        TaskKey: taskKey,
        fileUrl: fileUrl,
        ...options
    };
    
    try {
        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        });
        
        if (!response.ok) {
            if (response.status === 400) {
                const errorInfo = await response.json();
                throw new Error(`验证错误: ${errorInfo.Message}`);
            }
            throw new Error(`HTTP错误: ${response.status}`);
        }
        
        return await response.json();
    } catch (error) {
        console.error('请求错误:', error.message);
        throw error;
    }
}
```

## 迁移指南

### 从自定义验证迁移

如果您之前使用的是旧版本的自定义验证，请注意以下变更：

1. **错误消息格式**: 错误消息现在更加详细和结构化
2. **验证时机**: 验证现在在请求处理的更早阶段进行
3. **类型转换**: Pydantic会自动进行合理的类型转换
4. **性能提升**: 新的验证机制性能更优

### 兼容性说明

- API接口路径和基本参数保持不变
- 响应格式保持兼容
- 错误响应的结构保持一致，仅错误消息内容更加详细

## 技术细节

### 验证流程

1. **请求接收**: FastAPI接收HTTP请求
2. **JSON解析**: 解析请求体中的JSON数据
3. **Pydantic验证**: 使用对应的Pydantic模型进行验证
4. **错误处理**: 捕获ValidationError并格式化错误信息
5. **业务处理**: 验证通过后进行业务逻辑处理

### 性能优化

- 使用Pydantic的编译时优化
- 避免重复验证相同的数据结构
- 合理使用缓存机制
- 异步处理大量验证请求

## 相关文档

- [Pydantic官方文档](https://pydantic-docs.helpmanual.io/)
- [FastAPI数据验证](https://fastapi.tiangolo.com/tutorial/body/)
- [API接口文档](../README.md#api接口文档)
- [聊天服务文档](chat_service.md)
