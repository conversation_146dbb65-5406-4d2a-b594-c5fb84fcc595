# Python相关文件
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# 单元测试 / 覆盖率报告
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/

# 环境变量和配置文件
.env
.env.local
.env.*.local
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# 项目特定文件
logs/
*.log
*.log.*
credentials.json
config.json
.credentials

# 阿里云相关配置
.aliyun/
aliyun_credentials
*.pem
*.key
*.crt

# IDE和编辑器文件
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# poetry
poetry.lock

# celery
celerybeat-schedule
celerybeat.pid

# SageMath解析文件
*.sage.py

# 环境
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder项目设置
.spyderproject
.spyproject

# Rope项目设置
.ropeproject

# mkdocs文档
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre类型检查器
.pyre/

# pytype静态类型分析器
.pytype/

# Cython调试符号
cython_debug/

# 操作系统相关文件
# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# 部署和构建相关
.docker/
docker-compose.override.yml
.dockerignore
Dockerfile.local
*.pid
*.sock

# 备份文件
*.bak
*.backup
*.old
*.orig
*.tmp

# 压缩文件
*.7z
*.dmg
*.gz
*.iso
*.jar
*.rar
*.tar
*.zip

# 数据库文件
*.db
*.sqlite
*.sqlite3

# 临时文件
temp/
tmp/
.temp/
.tmp/

# 运行时文件
*.pid
*.lock

# 系统生成的文件
.well-known/

# 本地开发配置
local_settings.py
local_config.py

# 测试相关
.coverage
.pytest_cache/
.tox/

# 文档生成
docs/_build/
docs/build/

# 静态文件收集
static/
media/

# 缓存目录
.cache/
cache/

# Node.js相关（如果项目中有前端组件）
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm
.yarn-integrity

# 编辑器配置
.editorconfig

# 项目特定的忽略文件
# 可以根据具体需求添加更多规则