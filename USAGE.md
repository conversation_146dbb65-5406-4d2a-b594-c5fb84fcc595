# 千义听悟音频转写服务器使用指南

## 快速开始

### 1. 环境准备

确保您的系统满足以下要求：
- Python 3.10.18 或更高版本
- 网络连接（用于访问阿里云API）
- 阿里云账号和访问凭证

### 2. 安装依赖

```bash
# 进入项目目录
cd f:\Zixv\lcmp.server\ai

# 安装Python依赖包
pip install -r requirements.txt
```

### 3. 配置阿里云凭证

**方法一：环境变量（推荐）**

```bash
# Windows PowerShell
$env:ALIBABA_CLOUD_ACCESS_KEY_ID="your_access_key_id"
$env:ALIBABA_CLOUD_ACCESS_KEY_SECRET="your_access_key_secret"
$env:TINGWU_APP_KEY="your_app_key"

# Windows CMD
set ALIBABA_CLOUD_ACCESS_KEY_ID=your_access_key_id
set ALIBABA_CLOUD_ACCESS_KEY_SECRET=your_access_key_secret
set TINGWU_APP_KEY=your_app_key
```

**方法二：配置文件**

创建文件 `./config/credentials.conf`：

```ini
[default]
# 阿里云访问凭证
access_key_id = your_access_key_id
access_key_secret = your_access_key_secret
region_id = cn-beijing

# 千义听悟项目的AppKey
app_key = your_app_key
```

### 4. 启动服务器

```bash
# 开发模式（Flask内置服务器）
python -m tingwu_server.app

# 生产模式（Waitress服务器）
python -m tingwu_server.server
```

服务器默认监听端口 `5579`，可以通过环境变量配置：

```bash
# 自定义端口和主机
set TINGWU_HOST=0.0.0.0
set TINGWU_PORT=8080
set TINGWU_THREADS=8
```

### 5. 验证服务

**健康检查**

```bash
curl http://localhost:5579/health
```

预期响应：
```json
{
  "status": "healthy",
  "service": "tingwu-transcription-server",
  "version": "1.0.0"
}
```

## API使用示例

### 创建转写任务

**基本请求**

```bash
curl -X POST http://localhost:5579/ai/aliyun/voice/tingwu/new \
  -H "Content-Type: application/json" \
  -d '{
    "TaskKey": "my_task_123",
    "fileUrl": "http://example.com/audio.mp3"
  }'
```

**完整参数请求**

```bash
curl -X POST http://localhost:5579/ai/aliyun/voice/tingwu/new \
  -H "Content-Type: application/json" \
  -d '{
    "TaskKey": "advanced_task_456",
    "fileUrl": "http://example.com/meeting.wav",
    "sourceLanguage": "cn",
    "sampleRate": 16000,
    "speakerCount": 3,
    "diarizationEnabled": true,
    "translationEnabled": false,
    "autoChaptersEnabled": true,
    "summarizationEnabled": true
  }'
```

**Python代码示例**

```python
import requests
import json

# 请求数据
data = {
    "TaskKey": "python_task_789",
    "fileUrl": "http://example.com/audio.mp3",
    "sourceLanguage": "cn",
    "sampleRate": 16000,
    "diarizationEnabled": True
}

# 发送请求
response = requests.post(
    "http://localhost:5579/ai/aliyun/voice/tingwu/new",
    headers={"Content-Type": "application/json"},
    json=data
)

# 处理响应
if response.status_code == 200:
    result = response.json()
    if result["Code"] == "0":
        task_id = result["Data"]["TaskId"]
        print(f"任务创建成功，TaskId: {task_id}")
    else:
        print(f"任务创建失败: {result['Message']}")
else:
    print(f"请求失败，状态码: {response.status_code}")
```

## 命令行工具使用

### 交互式模式

```bash
# 启动交互式CLI
python -m tingwu_server.cli_tool
```

按提示输入任务信息即可创建转写任务。

### 命令行模式

```bash
# 创建转写任务
python -m tingwu_server.cli_tool \
  --task-key cli_task_001 \
  --file-url http://example.com/audio.mp3 \
  --source-language cn \
  --sample-rate 16000

# 测试API连接
python -m tingwu_server.cli_tool --test

# 查看帮助
python -m tingwu_server.cli_tool --help
```

## 测试服务

### 健康检查

```bash
# 检查服务是否正常运行
curl http://localhost:5579/health
```

### 手动测试API

```bash
# 使用curl测试API
curl -X POST http://localhost:5579/ai/aliyun/voice/tingwu/new \
  -H "Content-Type: application/json" \
  -d '{"TaskKey": "test_123", "fileUrl": "http://example.com/audio.mp3"}'
```

## 配置选项

### 环境变量

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| TINGWU_HOST | 0.0.0.0 | 服务器监听主机 |
| TINGWU_PORT | 5579 | 服务器监听端口 |
| TINGWU_THREADS | 4 | 工作线程数 |
| LOG_LEVEL | INFO | 日志级别 |
| TINGWU_APP_KEY | your_app_key | 千义听悟应用密钥 |

### 自定义配置

复制 `config_example.py` 为 `config.py` 并修改配置：

```bash
cp config_example.py config.py
# 编辑 config.py 文件
```

## 常见问题

### Q1: 服务器启动失败

**可能原因：**
- 端口被占用
- Python版本不兼容
- 依赖包未安装

**解决方案：**
```bash
# 检查端口占用
netstat -ano | findstr :5579

# 更换端口启动
set TINGWU_PORT=8080
python -m tingwu_server.server

# 重新安装依赖
pip install -r requirements.txt --force-reinstall
```

### Q2: API调用认证失败

**错误信息：**
```
unable to load credentials from any of the providers
```

**解决方案：**
1. 检查环境变量是否正确设置
2. 验证AccessKey是否有效
3. 确认网络连接正常

```bash
# 检查环境变量
echo $env:ALIBABA_CLOUD_ACCESS_KEY_ID
echo $env:ALIBABA_CLOUD_ACCESS_KEY_SECRET
```

### Q3: 音频文件无法访问

**可能原因：**
- URL格式错误
- 文件不存在
- 网络连接问题
- 文件格式不支持

**解决方案：**
```bash
# 测试文件是否可访问
curl -I http://example.com/audio.mp3

# 检查文件格式
# 支持格式：MP3, WAV, M4A, AAC, FLAC, OGG, WMA, MP4等
```

### Q4: 转写任务状态查询

目前服务器只提供任务创建功能，任务状态查询需要：

1. 保存返回的TaskId
2. 使用阿里云控制台查看任务状态
3. 或者扩展服务器添加状态查询接口

## 性能优化

### 生产环境建议

1. **使用反向代理**
```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://127.0.0.1:5579;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

2. **调整线程数**
```bash
# 根据CPU核心数调整
set TINGWU_THREADS=8
python -m tingwu_server.server
```

3. **启用日志轮转**
```python
# 在config.py中配置
LOG_MAX_BYTES = 10485760  # 10MB
LOG_BACKUP_COUNT = 5
```

### 监控和维护

1. **监控服务状态**
```bash
# 定期检查健康状态
curl http://localhost:5579/health
```

2. **查看日志**
```bash
# 实时查看日志
tail -f tingwu_server.log

# 搜索错误日志
findstr "ERROR" tingwu_server.log
```

3. **性能监控**
- 监控CPU和内存使用率
- 监控网络连接数
- 监控API响应时间

## 安全注意事项

1. **凭证安全**
   - 不要在代码中硬编码AccessKey
   - 使用环境变量或凭证文件
   - 定期轮换访问密钥

2. **网络安全**
   - 生产环境使用HTTPS
   - 配置防火墙规则
   - 限制访问来源IP

3. **输入验证**
   - 服务器已内置输入验证
   - 客户端也应进行验证
   - 注意文件URL的安全性

## 扩展开发

### 添加新功能

1. **任务状态查询**
```python
# 在tingwu.py中添加
def get_task_status(self, task_id: str):
    # 实现状态查询逻辑
    pass
```

2. **结果获取**
```python
# 添加结果获取接口
def get_transcription_result(self, task_id: str):
    # 实现结果获取逻辑
    pass
```

3. **批量处理**
```python
# 添加批量任务创建
def create_batch_tasks(self, tasks: List[Dict]):
    # 实现批量处理逻辑
    pass
```

### 自定义配置

参考 `config_example.py` 创建自定义配置类，支持：
- 不同环境配置
- 自定义参数验证
- 扩展功能开关

## 技术支持

如果遇到问题，请：

1. 查看日志文件 `tingwu_server.log`
2. 运行测试工具 `python test_server.py`
3. 检查配置 `python config_example.py`
4. 参考阿里云官方文档

相关链接：
- [千义听悟官方文档](https://help.aliyun.com/document_detail/378659.html)
- [API参考文档](https://next.api.aliyun.com/document/tingwu/2023-09-30/CreateTask)
- [凭证配置指南](https://help.aliyun.com/document_detail/378659.html)